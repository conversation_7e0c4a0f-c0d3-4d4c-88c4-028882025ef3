import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class JinaParser implements ProductParser {
  name = 'jina'
  private apiKey: string | undefined
  private baseUrl = 'https://r.jina.ai'
  private classifierUrl = 'https://api.jina.ai/v1/classify'

  constructor() {
    this.apiKey = process.env.JINA_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Jina API key not configured')
      }

      // 使用增强的 Jina AI 功能
      const searchParams = new URLSearchParams({
        // 使用 ReaderLM-v2 获得更高质量的解析结果
        'x-use-readerlm': 'true',
        // 启用图片描述功能
        'x-image-caption': 'true',
        // 在末尾收集所有图片信息
        'x-gather-images': 'at-end',
        // 排除常见的干扰元素
        'x-exclude-selectors': 'header,footer,nav,.navigation,.menu,.sidebar,.ads,.advertisement,.cookie-banner,.popup,.modal',
        // 等待主要内容加载
        'x-wait-for-selector': 'main,article,.content,.main-content,h1,[class*="hero"],[class*="landing"]',
        // 使用 Chromium 引擎获得更好的渲染效果
        'x-browser-engine': 'chromium',
        // 设置超时时间
        'x-timeout': '20'
      })

      const response = await fetch(`${this.baseUrl}/${url}?${searchParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          'User-Agent': 'ProductParser/2.0'
        },
        signal: AbortSignal.timeout(45000)
      })

      if (!response.ok) {
        throw new Error(`Jina API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const parsedInfo = await this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private async extractProductInfo(data: any, url: string): Promise<ParsedProductInfo> {
    // Jina AI 返回的数据结构: { data: { title, description, content, url, html } }
    const jinaData = data.data || data
    const htmlContent = jinaData.html || ''
    const markdownContent = jinaData.content || ''

    // 1. 获取标题 - 优先使用 Jina AI 提取的标题
    let title = jinaData.title
    if (!title && htmlContent) {
      title = this.extractTitle(htmlContent)
    }

    // 2. 清理标题，但保持简单
    const productName = this.cleanTitle(title) || this.generateNameFromUrl(url)

    // 3. 从 Markdown 内容中提取更丰富的信息
    const enhancedInfo = this.extractFromMarkdown(markdownContent)

    // 4. 获取描述 - 优先使用增强提取的描述
    let description = enhancedInfo.description || jinaData.description
    if (!description && htmlContent) {
      description = this.extractDescription(htmlContent)
    }

    // 5. 提取标语 - 优先使用增强提取的标语
    const tagline = enhancedInfo.tagline || this.extractSimpleTagline(htmlContent, description)

    // 6. 提取图片 - 使用多种方法
    const logoUrl = this.extractFavicon(htmlContent, url)
    const coverImageUrl = this.extractOgImage(htmlContent, url) ||
                          this.extractImageFromMarkdown(markdownContent, url)

    // 7. 使用 AI 分类器进行二次处理和验证
    const refinedInfo = await this.refineWithAI({
      name: enhancedInfo.name || productName,
      tagline: tagline,
      description: description,
      url: url
    })

    return {
      name: refinedInfo.name,
      tagline: refinedInfo.tagline,
      description: refinedInfo.description,
      logoUrl: logoUrl,
      coverImageUrl: coverImageUrl
    }
  }

  private extractTitle(content: string): string | undefined {
    // 尝试从内容中提取标题
    const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i)
    if (titleMatch) {
      return titleMatch[1].trim()
    }

    // 尝试从h1标签提取
    const h1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i)
    if (h1Match) {
      return h1Match[1].trim()
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 尝试从meta description提取
    const metaMatch = content.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (metaMatch) {
      return metaMatch[1].trim()
    }

    return undefined
  }

  private extractRichDescription(content: string, metaDescription?: string): string | undefined {
    // 如果有 meta description 且足够详细，优先使用
    if (metaDescription && metaDescription.length > 50) {
      return metaDescription
    }

    // 尝试从多个来源提取更丰富的描述
    const descriptions: string[] = []

    // 1. Meta description
    if (metaDescription) {
      descriptions.push(metaDescription)
    }

    // 2. 尝试从 og:description 提取
    const ogDescMatch = content.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogDescMatch) {
      descriptions.push(ogDescMatch[1].trim())
    }

    // 3. 尝试从第一个段落提取
    const paragraphMatches = content.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/gi)
    if (paragraphMatches) {
      for (const match of paragraphMatches.slice(0, 3)) {
        const text = match.replace(/<[^>]*>/g, '').trim()
        if (text.length > 50 && text.length < 300 && !text.includes('cookie') && !text.includes('privacy')) {
          descriptions.push(text)
        }
      }
    }

    // 4. 尝试从主要内容区域提取
    const mainContentMatch = content.match(/<main[^>]*>([\s\S]*?)<\/main>/i) ||
                             content.match(/<article[^>]*>([\s\S]*?)<\/article>/i) ||
                             content.match(/<section[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/section>/i)

    if (mainContentMatch) {
      const mainText = mainContentMatch[1].replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
      const sentences = mainText.split(/[.!?]+/).filter(s => s.trim().length > 30)
      if (sentences.length > 0) {
        const firstSentences = sentences.slice(0, 2).join('. ').trim()
        if (firstSentences.length > 50 && firstSentences.length < 400) {
          descriptions.push(firstSentences + (firstSentences.endsWith('.') ? '' : '.'))
        }
      }
    }

    // 选择最好的描述（优先选择长度适中且信息丰富的）
    const bestDescription = descriptions.find(desc => desc.length > 100 && desc.length < 300) ||
                           descriptions.find(desc => desc.length > 50 && desc.length < 500) ||
                           descriptions[0]

    return bestDescription
  }

  private cleanTitle(title: string | undefined): string | undefined {
    if (!title) return undefined

    let cleanedTitle = title.trim()

    // 简单的清理规则 - 移除常见的分隔符后的内容
    // 移除 "| 网站名称"
    cleanedTitle = cleanedTitle.replace(/\s*\|\s*.+$/, '')

    // 移除 "- 网站名称"
    cleanedTitle = cleanedTitle.replace(/\s*-\s*.+$/, '')

    // 移除 "· 网站名称"
    cleanedTitle = cleanedTitle.replace(/\s*·\s*.+$/, '')

    // 移除 "— 网站名称"
    cleanedTitle = cleanedTitle.replace(/\s*[—–]\s*.+$/, '')

    // 处理冒号的情况 - 如果冒号前面是短的产品名称，保留前面部分
    const colonMatch = cleanedTitle.match(/^([^:]{2,20}):\s*.+$/)
    if (colonMatch) {
      const beforeColon = colonMatch[1].trim()
      // 如果冒号前面看起来像产品名称（短且不包含常见的描述词），就使用它
      if (beforeColon.length >= 2 && beforeColon.length <= 20 &&
          !beforeColon.toLowerCase().includes('welcome') &&
          !beforeColon.toLowerCase().includes('home') &&
          !beforeColon.toLowerCase().includes('official')) {
        cleanedTitle = beforeColon
      }
    }

    cleanedTitle = cleanedTitle.trim()

    // 如果清理后太短或为空，返回 undefined 让调用者使用域名
    if (cleanedTitle.length < 2) {
      return undefined
    }

    // 如果太长，简单截取
    if (cleanedTitle.length > 60) {
      cleanedTitle = cleanedTitle.substring(0, 57) + '...'
    }

    return cleanedTitle
  }

  private extractSimpleTagline(content: string, description?: string): string | undefined {
    // 1. 如果描述不太长，直接使用描述的第一句话
    if (description && description.length < 120) {
      return description
    }

    // 2. 从描述中提取第一句话
    if (description) {
      const firstSentence = description.split(/[.!?]/)[0].trim()
      if (firstSentence.length > 10 && firstSentence.length < 120) {
        return firstSentence
      }
    }

    // 3. 尝试从第一个 H2 标签提取
    const h2Match = content.match(/<h2[^>]*>([^<]+)<\/h2>/i)
    if (h2Match) {
      const h2Text = h2Match[1].trim()
      if (h2Text.length > 5 && h2Text.length < 100) {
        return h2Text
      }
    }

    return undefined
  }

  private extractTagline(content: string, description?: string): string | undefined {
    // 1. 尝试从主要标题下的副标题提取（优先简短的标语）
    const h1Matches = content.match(/<h1[^>]*>([\s\S]*?)<\/h1>/gi)
    if (h1Matches) {
      for (const h1Match of h1Matches) {
        // 查找 h1 后面的 p 标签或 h2 标签
        const h1Index = content.indexOf(h1Match)
        const afterH1 = content.substring(h1Index + h1Match.length, h1Index + h1Match.length + 1000)

        const nextPMatch = afterH1.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/i)
        if (nextPMatch) {
          const pText = nextPMatch[1].replace(/<[^>]*>/g, '').trim()
          if (pText.length > 10 && pText.length < 120 &&
              !pText.includes('cookie') &&
              !pText.includes('privacy') &&
              !pText.toLowerCase().includes('sign up') &&
              !pText.toLowerCase().includes('learn more')) {
            return pText
          }
        }
      }
    }

    // 2. 尝试从主要标题下的副标题提取
    const h2Matches = content.match(/<h2[^>]*>([^<]+)<\/h2>/gi)
    if (h2Matches) {
      for (const h2Match of h2Matches.slice(0, 3)) {
        const h2Text = h2Match.replace(/<[^>]*>/g, '').trim()
        if (h2Text.length > 10 && h2Text.length < 100 &&
            !h2Text.toLowerCase().includes('menu') &&
            !h2Text.toLowerCase().includes('navigation') &&
            !h2Text.toLowerCase().includes('footer') &&
            !h2Text.toLowerCase().includes('products') &&
            !h2Text.toLowerCase().includes('solutions')) {
          return h2Text
        }
      }
    }

    // 3. 尝试从 hero section 的简短段落提取
    const heroMatches = [
      content.match(/<section[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/section>/i),
      content.match(/<div[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/div>/i)
    ]

    for (const heroMatch of heroMatches) {
      if (heroMatch) {
        const heroContent = heroMatch[1]
        const pMatches = heroContent.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/gi)
        if (pMatches) {
          for (const pMatch of pMatches.slice(0, 2)) {
            const pText = pMatch.replace(/<[^>]*>/g, '').trim()
            if (pText.length > 15 && pText.length < 120 &&
                !pText.includes('cookie') &&
                !pText.includes('privacy') &&
                !pText.toLowerCase().includes('sign up') &&
                !pText.toLowerCase().includes('learn more') &&
                !pText.toLowerCase().includes('get started')) {
              return pText
            }
          }
        }
      }
    }

    // 4. 尝试使用简短的 meta description
    if (description && description.length > 10 && description.length < 100) {
      return description
    }

    // 5. 尝试从 og:description 提取
    const ogDescMatch = content.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogDescMatch) {
      const ogDesc = ogDescMatch[1].trim()
      if (ogDesc.length > 10 && ogDesc.length < 120 && ogDesc !== description) {
        return ogDesc
      }
    }

    // 6. 从较长的 description 中提取第一句话
    if (description && description.length > 100) {
      const firstSentence = description.split(/[.!?]/)[0].trim()
      if (firstSentence.length > 15 && firstSentence.length < 100) {
        return firstSentence + '.'
      }
    }

    // 7. 最后回退：使用截断的 description
    if (description && description.length > 10) {
      return description.length > 100 ? description.substring(0, 97) + '...' : description
    }

    return undefined
  }

  private extractFavicon(content: string, url: string): string | undefined {
    const baseUrl = new URL(url).origin

    // 1. 尝试从 link rel="icon" 提取
    const iconMatches = [
      // 标准 favicon
      content.match(/<link[^>]*rel=["\'](?:icon|shortcut icon)["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // Apple touch icon
      content.match(/<link[^>]*rel=["\']apple-touch-icon["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // 其他图标格式
      content.match(/<link[^>]*rel=["\']icon["\'][^>]*type=["\']image\/[^"']*["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i)
    ]

    for (const match of iconMatches) {
      if (match && match[1]) {
        const iconUrl = match[1]
        // 处理相对路径
        if (iconUrl.startsWith('//')) {
          return `https:${iconUrl}`
        } else if (iconUrl.startsWith('/')) {
          return `${baseUrl}${iconUrl}`
        } else if (iconUrl.startsWith('http')) {
          return iconUrl
        } else {
          return `${baseUrl}/${iconUrl}`
        }
      }
    }

    // 2. 回退到默认 favicon 路径
    return `${baseUrl}/favicon.ico`
  }

  private extractOgImage(content: string, url: string): string | undefined {
    const baseUrl = new URL(url).origin

    // 1. 尝试从 og:image 提取
    const ogImageMatch = content.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogImageMatch) {
      const imageUrl = ogImageMatch[1]
      // 处理相对路径
      if (imageUrl.startsWith('//')) {
        return `https:${imageUrl}`
      } else if (imageUrl.startsWith('/')) {
        return `${baseUrl}${imageUrl}`
      } else if (imageUrl.startsWith('http')) {
        return imageUrl
      } else {
        return `${baseUrl}/${imageUrl}`
      }
    }

    // 2. 尝试从 twitter:image 提取
    const twitterImageMatch = content.match(/<meta[^>]*name=["\']twitter:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (twitterImageMatch) {
      const imageUrl = twitterImageMatch[1]
      // 处理相对路径
      if (imageUrl.startsWith('//')) {
        return `https:${imageUrl}`
      } else if (imageUrl.startsWith('/')) {
        return `${baseUrl}${imageUrl}`
      } else if (imageUrl.startsWith('http')) {
        return imageUrl
      } else {
        return `${baseUrl}/${imageUrl}`
      }
    }

    // 3. 尝试从第一个有意义的图片提取（避免小图标）
    const imgMatches = content.match(/<img[^>]*src=["\']([^"']+)["\'][^>]*>/gi)
    if (imgMatches) {
      for (const imgMatch of imgMatches) {
        const srcMatch = imgMatch.match(/src=["\']([^"']+)["\']/)
        if (srcMatch) {
          const src = srcMatch[1]
          // 过滤掉小图标和明显的装饰性图片
          if (!src.includes('icon') &&
              !src.includes('logo') &&
              !src.includes('favicon') &&
              !src.includes('sprite') &&
              !src.includes('pixel') &&
              !src.endsWith('.svg') &&
              (src.includes('screenshot') ||
               src.includes('preview') ||
               src.includes('hero') ||
               src.includes('banner') ||
               imgMatch.includes('width') && !imgMatch.includes('width="16"') && !imgMatch.includes('width="32"'))) {
            // 处理相对路径
            if (src.startsWith('//')) {
              return `https:${src}`
            } else if (src.startsWith('/')) {
              return `${baseUrl}${src}`
            } else if (src.startsWith('http')) {
              return src
            } else {
              return `${baseUrl}/${src}`
            }
          }
        }
      }
    }

    return undefined
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }


}
